package com.leave.ink.ui.skija;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.hud.EventRenderSkija;
import com.leave.ink.events.hud.EventSkia2D;
import com.leave.ink.events.hud.EventSkiaProcess;
import com.leave.ink.ui.skija.utils.ImageHelper;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.render.projectiles.world.Projection;

public class SkiaProcess extends Skia {
    public static SkiaProcess INSTANCE;
    private final Projection projection = new Projection();
    
    public SkiaProcess() {
        EventManager.register(this);
        INSTANCE = this;  // 设置静态实例
    }
    
    @EventTarget
    public void onRender(EventRenderSkija eventRenderSkija) {

        if (skiaUtils == null) {
            System.err.println("SkijaUtils not initialized. Skipping render frame.");
            return;
        }

        try {
            skiaUtils.checkAndUpdateSurface();
            skiaUtils.beginFrame();

            CanvasStack canvasStack = new CanvasStack(skiaUtils);
            canvasStack.push();

            try {
                if (!Utils.isNull()) {
                    EventManager.call(new EventSkiaProcess(canvasStack));
                }
                // processRenderTasks(canvasStack); // Uncomment if needed
            } finally {
                canvasStack.pop();
            }

            skiaUtils.endFrame();
            // updatePerformanceStats(); // Uncomment if needed

        } catch (Exception e) {
            System.err.println("Error during Skia rendering: " + e.getMessage());
            e.printStackTrace();
            try {
                skiaUtils.endFrame();
            } catch (Exception ignored) {
                System.err.println("Warning: Error occurred during Skia endFrame cleanup after a rendering error: " + ignored.getMessage());
            }
        }

    }

    /**
     * 处理3D渲染事件，为Skia2D兼容层提供支持
     */
    @EventTarget
    public void onRender3D(EventRender3D event) {
        if (skiaUtils == null || Utils.isNull()) {
            return;
        }

        try {
            // 确保Skia上下文可用
            if (skiaUtils.surface == null || skiaUtils.canvas == null) {
                return;
            }

            // 为3D场景中的Skia渲染创建Canvas栈
            CanvasStack canvasStack = new CanvasStack(skiaUtils);
            canvasStack.push();

            try {
                // 触发Skia2D兼容层事件
                EventSkia2D eventSkia2D = new EventSkia2D(
                    canvasStack, 
                    projection, 
                    event.getPoseStack(), 
                    event.getPartialTicks()
                );
                EventManager.call(eventSkia2D);
            } finally {
                canvasStack.pop();
            }

        } catch (Exception e) {
            System.err.println("Error during Skia2D compatibility layer: " + e.getMessage());
            e.printStackTrace();
        }
    }

    public void cleanup() {

        ImageHelper.clearCache();
        if (skiaUtils != null) {
            skiaUtils.cleanup();
        }
        
        System.out.println("SkijaProcess资源已清理");
    }

}
