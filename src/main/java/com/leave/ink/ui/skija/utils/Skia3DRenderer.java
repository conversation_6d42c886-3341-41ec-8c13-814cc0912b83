package com.leave.ink.ui.skija.utils;

import com.leave.ink.Main;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaProcess;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;

import java.util.function.Consumer;

public class Skia3DRenderer implements IMinecraft {
    public record Render3DContext(CanvasStack canvasStack, Entity targetEntity, Vec3 worldPosition, Vec3 screenPosition,
                                  float scale, float distance) {
    }

    public static class RenderConfig {
        public float baseScale = 1.5f;
        public float yOffset = 0.55f;
        public float minDistance = 1.0f;
        public float scaleMultiplier = 0.25f;
        public boolean enableDistanceScaling = true;
        public boolean enableBillboard = true;
        
        public RenderConfig() {}
        
        public RenderConfig setBaseScale(float baseScale) {
            this.baseScale = baseScale;
            return this;
        }
        
        public RenderConfig setYOffset(float yOffset) {
            this.yOffset = yOffset;
            return this;
        }
        
        public RenderConfig setMinDistance(float minDistance) {
            this.minDistance = minDistance;
            return this;
        }
        
        public RenderConfig setScaleMultiplier(float scaleMultiplier) {
            this.scaleMultiplier = scaleMultiplier;
            return this;
        }
        
        public RenderConfig setEnableDistanceScaling(boolean enableDistanceScaling) {
            this.enableDistanceScaling = enableDistanceScaling;
            return this;
        }
        
        public RenderConfig setEnableBillboard(boolean enableBillboard) {
            this.enableBillboard = enableBillboard;
            return this;
        }
    }

    public static boolean render3D(CanvasStack canvasStack, Entity entity, RenderConfig config,
                                  Consumer<Render3DContext> renderer) {
        Vec3 worldPos = calculateWorldPosition(entity, config.yOffset);
        Vec3 screenPos = projectToScreen(worldPos);
        if (!SkiaCompatLayer.isOnScreen(screenPos)) {
            return false;
        }
        float distance = mc.player.distanceTo(entity);
        float scale = calculateScale(distance, config);
            try {
            Render3DContext context = new Render3DContext(
                    canvasStack, entity, worldPos, screenPos, scale, distance
            );
            renderer.accept(context);
            
            return true;
            
        } catch (Exception e) {
            System.err.println("Skia3DRenderer error: " + e.getMessage());
            return false;
        }
    }
    public static boolean render3D(CanvasStack canvasStack, Entity entity, Consumer<Render3DContext> renderer) {
        return render3D(canvasStack, entity, new RenderConfig(), renderer);
    }
    private static Vec3 calculateWorldPosition(Entity entity, float yOffset) {
        float partialTicks = mc.getFrameTime();
        double entityX = entity.xOld + (entity.getX() - entity.xOld) * partialTicks;
        double entityY = entity.yOld + (entity.getY() - entity.yOld) * partialTicks + entity.getEyeHeight() + yOffset;
        double entityZ = entity.zOld + (entity.getZ() - entity.zOld) * partialTicks;
        
        return new Vec3(entityX, entityY, entityZ);
    }
    private static float calculateScale(float distance, RenderConfig config) {
        if (!config.enableDistanceScaling) {
            return config.baseScale;
        }
        
        float adjustedDistance = distance * config.scaleMultiplier;
        if (adjustedDistance < config.minDistance) {
            adjustedDistance = config.minDistance;
        }
        
        return adjustedDistance / 100f * config.baseScale;
    }
    private static Vec3 projectToScreen(Vec3 worldPos) {
        try {
            if (Main.INSTANCE.projection == null) {
                return null;
            }
            
            EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
            Camera camera = renderManager.camera;
            
            Vec3 relativePos = new Vec3(
                worldPos.x - camera.getPosition().x,
                worldPos.y - camera.getPosition().y,
                worldPos.z - camera.getPosition().z
            );
            
            return Main.INSTANCE.projection.projectToScreen(relativePos);
            
        } catch (Exception e) {
            return null;
        }
    }

}
