package com.leave.ink.ui.skija.utils;

import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.render.projectiles.world.Projection;
import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;
import java.awt.Color;
import com.leave.ink.ui.skija.font.SkiaFont;

public class SkiaCompatLayer implements IMinecraft {

    public static Vec3 getEntityScreen2DPos(Entity entity, Projection projection, float partialTicks) {
        double entityX = entity.xOld + (entity.getX() - entity.xOld) * partialTicks;
        double entityY = entity.yOld + (entity.getY() - entity.yOld) * partialTicks + entity.getEyeHeight() + 0.55;
        double entityZ = entity.zOld + (entity.getZ() - entity.zOld) * partialTicks;
        
        Vec3 worldPos = new Vec3(entityX, entityY, entityZ);
        return projection.projectToScreen(worldPos);
    }
    public static float calculateDistanceScale(Entity entity, float baseScale, float minScale, float maxScale) {
        if (mc.player == null) return baseScale;
        
        float distance = mc.player.distanceTo(entity) * 0.25f;
        if (distance < 1F) distance = 1F;
        
        float scale = (distance / 100f) * baseScale;
        return Math.max(minScale, Math.min(maxScale, scale));
    }

    public static NameTagRenderInfo calculateNameTagInfo(Entity entity, String mainText, String subText, Vec3 screenPos, float scale) {
        float mainTextWidth = getTextWidth(mainText, 18) * scale;
        float subTextWidth = getTextWidth(subText, 14) * scale;
        float maxWidth = Math.max(mainTextWidth, subTextWidth);
        float width = maxWidth * 0.5f;
        float height = (getTextHeight(18) + 16) * scale;

        float x = (float) screenPos.x - width;
        float y = (float) screenPos.y - height;
        
        return new NameTagRenderInfo(x, y, width * 2, height, scale);
    }

    public static void drawNameTagBackground(CanvasStack canvasStack, float x, float y, float width, float height, Color backgroundColor) {
        SkiaRender.drawRect(canvasStack, x, y, width, height, backgroundColor.getRGB());
    }

    public static void drawNameTagHealthBar(CanvasStack canvasStack, float x, float y, float width, float height, float healthRate, Color healthColor) {
        float healthWidth = width * healthRate;
        SkiaRender.drawRect(canvasStack, x, y, healthWidth, height, healthColor.getRGB());
    }

    public static void drawNameTagText(CanvasStack canvasStack, String text, float x, float y, Color textColor, int fontSize) {
        drawText(canvasStack, text, x, y, fontSize, textColor);
    }

    public static float getTextWidth(String text, int fontSize) {
        return getStringWidth(text, fontSize);
    }

    public static float getTextHeight(int fontSize) {
        return getStringHeight(fontSize);
    }

    public static void drawText(CanvasStack canvasStack, String text, float x, float y, int size, Color textColor) {
        SkiaFont font = SkiaFontManager.getFont(size);
        font.drawText(canvasStack, text, x, y, textColor.getRGB());
    }

    public static float getStringWidth(String text, int size) {
        SkiaFont font = SkiaFontManager.getFont(size);
        return font.getWidth(text);
    }

    public static float getStringHeight(int size) {
        SkiaFont font = SkiaFontManager.getFont(size);
        return font.getActualHeight();
    }

    public static boolean isOnScreen(Vec3 screenPos) {
        if (screenPos == null) return false;
        
        double x = screenPos.x;
        double y = screenPos.y;
        double z = screenPos.z;

        if (Double.isNaN(x) || Double.isNaN(y) || Double.isNaN(z)) return false;
        if (z < 0 || z > 1) return false;
        int screenWidth = mc.getWindow().getGuiScaledWidth();
        int screenHeight = mc.getWindow().getGuiScaledHeight();
        int margin = 50; // 边界容差
        
        return x >= -margin && x <= screenWidth + margin && y >= -margin && y <= screenHeight + margin;
    }

    public static class NameTagRenderInfo {
        public final float x, y, width, height, scale;
        
        public NameTagRenderInfo(float x, float y, float width, float height, float scale) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.scale = scale;
        }
    }
} 