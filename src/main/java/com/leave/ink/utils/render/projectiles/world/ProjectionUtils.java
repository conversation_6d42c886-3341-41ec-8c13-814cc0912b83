package com.leave.ink.utils.render.projectiles.world;

import com.leave.ink.utils.wrapper.IMinecraft;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.phys.Vec3;

public class ProjectionUtils implements IMinecraft {
    public static Vec3 calculateWorldPosition(Entity entity, float yOffset) {
        float partialTicks = mc.getFrameTime();
        double entityX = entity.xOld + (entity.getX() - entity.xOld) * partialTicks;
        double entityY = entity.yOld + (entity.getY() - entity.yOld) * partialTicks + entity.getEyeHeight() + yOffset;
        double entityZ = entity.zOld + (entity.getZ() - entity.zOld) * partialTicks;

        return new Vec3(entityX, entityY, entityZ);
    }
}
