package com.leave.ink.utils.render.projectiles.world;

import com.darkmagician6.eventapi.EventManager;
import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventWorld;
import com.leave.ink.features.module.modules.render.esp.ESPUtils;
import com.leave.ink.utils.wrapper.IMinecraft;
import lombok.Getter;
import net.minecraft.client.Camera;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.phys.Vec3;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/08/03
 * @param <T>
 */
public class ProjectionSystem<T> implements IMinecraft {
    private Projection projection = new Projection();
    @Getter
    private final Map<T, ProjectionData> dataMap = new HashMap<>();
    public ProjectionSystem() {
        EventManager.register(this);
    }

    @EventTarget
    public void onWorld(EventWorld e) {
        dataMap.clear();
    }


    public Vec3 projectToScreen(Vec3 worldPos) {
        try {
            if (Main.INSTANCE.projection == null) {
                return null;
            }

            EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
            Camera camera = renderManager.camera;

            Vec3 relativePos = new Vec3(
                    worldPos.x - camera.getPosition().x,
                    worldPos.y - camera.getPosition().y,
                    worldPos.z - camera.getPosition().z
            );

            return Main.INSTANCE.projection.projectToScreen(relativePos);

        } catch (Exception e) {
            return null;
        }
    }
    public void update(T key, ProjectionData data) {
        if(dataMap.get(key) == null)
            dataMap.put(key, data);
        else
            dataMap.replace(key, data);
    }
    public static class ProjectionScreenData extends ProjectionData {
        public Vec3 pos;
        public ProjectionScreenData(Vec3 pos) {
            this.pos = pos;
        }
    }
    public static class ProjectionData {

    }
}
