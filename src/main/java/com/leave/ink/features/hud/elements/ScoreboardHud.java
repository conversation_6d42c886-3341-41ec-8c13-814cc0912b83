package com.leave.ink.features.hud.elements;

import com.leave.ink.Main;
import com.leave.ink.features.hud.AbsHudElement;
import com.leave.ink.features.hud.main.ElementsHud;
import com.leave.ink.features.module.modules.other.NameProtect;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ButtonSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.IconFont;
import com.leave.ink.ui.skija.font.SkiaFont;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.Utils;
import io.github.humbleui.skija.ClipMode;
import net.minecraft.ChatFormatting;
import net.minecraft.network.chat.Component;
import net.minecraft.world.scores.*;

import java.awt.Color;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

public class ScoreboardHud extends AbsHudElement {
    private static final Pattern COLOR_PATTERN = Pattern.compile("(?i)§[0-9A-FK-OR]");

    @SettingInfo(name = { @Text(label = "Background", language = Language.English), @Text(label = "显示背景", language = Language.Chinese) })
    public static final BooleanSetting background = new BooleanSetting(true);

    @SettingInfo(name = { @Text(label = "ShowNumbers", language = Language.English), @Text(label = "显示数字", language = Language.Chinese) })
    public static final BooleanSetting showNumbers = new BooleanSetting(true);

    @SettingInfo(name = { @Text(label = "Delete", language = Language.English), @Text(label = "删除", language = Language.Chinese) })
    public final ButtonSetting delete = new ButtonSetting() {
        @Override
        public void onClickedButton() {
            Main.INSTANCE.hudManager.removeElement(getElementName());
        }
    };

    public ScoreboardHud() {
        super("Scoreboard", 0.85, 0.3, 150, 200);
        registerSetting(background, showNumbers, delete);
    }

    @Override
    protected void processDraw(CanvasStack canvasStack) {
        if (mc.player == null || mc.level == null) return;
        Scoreboard scoreboard = mc.level.getScoreboard();
        Objective objective = scoreboard.getDisplayObjective(1);
        if (objective == null) return;
        List<Score> scoreList = scoreboard.getPlayerScores(objective).stream()
                .filter(score -> !score.getOwner().startsWith("#"))
                .collect(Collectors.toList());

        if (scoreList.size() > 15) {
            scoreList = scoreList.subList(scoreList.size() - 15, scoreList.size());
        }
        if (scoreList.isEmpty()) return;
        String titleText = Utils.getStringFromFormattedCharSequence(objective.getDisplayName().getVisualOrderText());
        float textWidth = getCleanTextWidth(titleText);

        List<ScoreboardEntry> displayList = new ArrayList<>();
        for (Score score : scoreList) {
            String coloredName = Utils.getStringFromFormattedCharSequence(PlayerTeam.formatNameForTeam(scoreboard.getPlayersTeam(score.getOwner()), Component.literal(score.getOwner())).getVisualOrderText());
            NameProtect nameProtect = (NameProtect) Main.INSTANCE.moduleManager.getModule("NameProtect");
            if (nameProtect.isEnable() && mc.player != null && coloredName.contains(mc.player.getName().getString())) {
                coloredName = coloredName.replace(mc.player.getName().getString(), "Hide");
            }

            displayList.add(new ScoreboardEntry(score, coloredName));
            textWidth = Math.max(textWidth, getCleanTextWidth(showNumbers.getValue() ? (coloredName + " " + score.getScore()) : coloredName));
        }
        float width = textWidth + 10;
        float height = 18 + (displayList.size() * 8);
        setWidth(width);
        setHeight(height);

        if (ElementsHud.shadow.getValue()) {
            canvasStack.push();
            SkiaRender.scissorRoundedRect(canvasStack, 0, 0, width, height, 16, ClipMode.DIFFERENCE);
            SkiaRender.drawRoundedRectWithShadow(canvasStack, 0, 0, width, height, 16);
            canvasStack.pop();
        }

        if (background.getValue()) {
            if (ElementsHud.blur.getValue()) {
                SkiaRender.drawBlurRect(canvasStack, 0, 0, width, height, 16, 12);
            }
            SkiaRender.drawRoundedRect(canvasStack, 0, 0, width, height, 16, new Color(24, 24, 24, 120).getRGB());
        }

        float y = 5;
        // 使用专门的ScoreboardHud渲染方法
        float titleX = (float) ((getWidth() - getCleanTextWidth(titleText)) / 2);
        drawScoreboardText(canvasStack, titleText, titleX, y, ElementsHud.fontShadow.getValue());
        y += 8;

        for (int i = displayList.size() - 1; i >= 0; i--) {
            ScoreboardEntry entry = displayList.get(i);
            String text = entry.coloredName();
            // 使用专门的ScoreboardHud渲染方法
            drawScoreboardText(canvasStack, text, 5, y, ElementsHud.fontShadow.getValue());

            if (showNumbers.getValue()) {
                String score = String.valueOf(entry.score().getScore());
                float scoreX = width - 5 - getCleanTextWidth(score);
                // 使用专门的ScoreboardHud渲染方法
                drawScoreboardText(canvasStack, score, scoreX, y, ElementsHud.fontShadow.getValue());
            }
            y += 8;
        }
    }

    /**
     * 专门为ScoreboardHud设计的文本渲染方法
     * 能够正确处理图标和颜色代码的混合渲染
     */
    private void drawScoreboardText(CanvasStack canvasStack, String text, float x, float y, boolean shadow) {
        if (text == null || text.isEmpty()) return;

        float currentX = x;
        StringBuilder currentSegment = new StringBuilder();
        int currentColor = Color.WHITE.getRGB();

        for (int i = 0; i < text.length(); i++) {
            char c = text.charAt(i);
            String charStr = String.valueOf(c);

            // 处理颜色代码
            if (c == '§' && i + 1 < text.length()) {
                // 先渲染当前累积的文本段
                if (!currentSegment.isEmpty()) {
                    currentX = renderTextSegment(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow, false);
                    currentSegment.setLength(0);
                }

                char colorChar = text.charAt(i + 1);
                // 处理十六进制颜色代码
                if (colorChar == 'x' && i + 13 < text.length()) {
                    try {
                        StringBuilder hexColor = new StringBuilder();
                        for (int j = 2; j < 14; j += 2) {
                            if (i + j + 1 < text.length() && text.charAt(i + j) == '§') {
                                hexColor.append(text.charAt(i + j + 1));
                            }
                        }
                        if (hexColor.length() == 6) {
                            currentColor = 0xFF000000 | Integer.parseInt(hexColor.toString(), 16);
                            i += 13;
                            continue;
                        }
                    } catch (NumberFormatException ignored) {
                    }
                }

                // 处理普通颜色代码
                ChatFormatting format = ChatFormatting.getByCode(colorChar);
                if (format != null) {
                    if (format.isColor()) {
                        Integer color = format.getColor();
                        if (color != null) {
                            currentColor = color | 0xFF000000;
                        }
                    } else if (format == ChatFormatting.RESET) {
                        currentColor = Color.WHITE.getRGB();
                    }
                }
                i++; // 跳过颜色代码字符
                continue;
            }

            // 处理图标字符
            if (isIconChar(charStr)) {
                // 先渲染当前累积的文本段
                if (!currentSegment.isEmpty()) {
                    currentX = renderTextSegment(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow, false);
                    currentSegment.setLength(0);
                }

                // 渲染图标
                String iconChar = IconFont.getIconChar(charStr);
                int iconColor = getIconColor(charStr, currentColor);
                currentX = renderTextSegment(canvasStack, iconChar, currentX, y, iconColor, shadow, true);
            } else {
                // 普通字符，添加到当前段
                currentSegment.append(c);
            }
        }

        // 渲染最后剩余的文本段
        if (!currentSegment.isEmpty()) {
            renderTextSegment(canvasStack, currentSegment.toString(), currentX, y, currentColor, shadow, false);
        }
    }

    /**
     * 渲染单个文本段（普通文本或图标）
     */
    private float renderTextSegment(CanvasStack canvasStack, String text, float x, float y, int color, boolean shadow, boolean isIcon) {
        if (text.isEmpty()) return x;

        SkiaFont font = isIcon ? SkiaFontManager.getIconFont14() : SkiaFontManager.getDefaultFont14();
        font.drawText(canvasStack, text, x, y, color, shadow);
        return x + font.getWidth(text);
    }

    /**
     * 检查字符是否为图标字符
     */
    private boolean isIconChar(String charStr) {
        return "✓".equals(charStr) || "\u2713".equals(charStr) || "\u2714".equals(charStr) ||
               "✗".equals(charStr) || "\u2717".equals(charStr) || "\u2716".equals(charStr) || "\u2718".equals(charStr) ||
               "♥".equals(charStr) || "❤".equals(charStr) || "\u2665".equals(charStr) || "\u2764".equals(charStr);
    }

    /**
     * 获取图标的颜色
     */
    private int getIconColor(String iconChar, int defaultColor) {
        // 绿色勾号
        if ("✓".equals(iconChar) || "\u2713".equals(iconChar) || "\u2714".equals(iconChar)) {
            return new Color(85, 255, 85).getRGB();
        }
        // 红色叉号
        if ("✗".equals(iconChar) || "\u2717".equals(iconChar) || "\u2716".equals(iconChar) || "\u2718".equals(iconChar)) {
            return new Color(255, 85, 85).getRGB();
        }
        // 其他图标使用默认颜色
        return defaultColor;
    }

    private float getCleanTextWidth(String text) {
        if (text == null || text.isEmpty()) return 0;

        // 如果包含图标，使用IconFont的宽度计算方法
        if (IconFont.containsIcons(text)) {
            return IconFont.getTextWidthWithIcons(text);
        }

        // 否则使用普通的颜色代码清理方法
        return SkiaFontManager.getDefaultFont14().getWidth(COLOR_PATTERN.matcher(text).replaceAll(""));
    }

    private record ScoreboardEntry(Score score, String coloredName) {}
}