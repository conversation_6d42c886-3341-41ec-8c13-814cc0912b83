package com.leave.ink.features.module.modules.render.esp;

import com.leave.ink.utils.render.projectiles.world.ProjectionSystem;
import com.leave.ink.utils.wrapper.IMinecraft;
import com.mojang.blaze3d.platform.GlStateManager;
import com.mojang.blaze3d.systems.RenderSystem;
import com.mojang.blaze3d.vertex.DefaultVertexFormat;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.blaze3d.vertex.VertexConsumer;
import com.mojang.blaze3d.vertex.VertexFormat;
import net.minecraft.client.Camera;
import net.minecraft.client.Minecraft;
import net.minecraft.client.model.geom.ModelPart;
import net.minecraft.client.renderer.GameRenderer;
import net.minecraft.client.renderer.MultiBufferSource;
import net.minecraft.client.renderer.RenderStateShard;
import net.minecraft.client.renderer.RenderType;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.phys.AABB;
import net.minecraft.world.phys.Vec3;
import org.joml.Matrix4f;
import org.joml.Vector4f;

import java.util.List;

/**
 * <AUTHOR>
 * @since 2025/04/06
 */
public class ESPUtils implements IMinecraft {
    public static class ESPU extends ProjectionSystem.ProjectionData {
        public LivingEntity entity;
        public boolean shouldDraw = true;
        public float leftPoint;
        public float topPoint;
        public float rightPoint;
        public float bottomPoint;

        public ESPU(LivingEntity entity, float leftPoint, float topPoint, float rightPoint, float bottomPoint) {
            this.entity = entity;
            this.leftPoint = leftPoint;
            this.topPoint = topPoint;
            this.rightPoint = rightPoint;
            this.bottomPoint = bottomPoint;
        }
    }
    public static final RenderStateShard.DepthTestStateShard NO_DEPTH_TEST = new RenderStateShard.DepthTestStateShard("always", 519);
    public static final RenderStateShard.CullStateShard NO_CULL = new RenderStateShard.CullStateShard(false);

    public static final RenderStateShard.TransparencyStateShard NO_TRANSPARENCY = new RenderStateShard.TransparencyStateShard("no_transparency", RenderSystem::disableBlend, () -> {});
    public static final RenderStateShard.ShaderStateShard POSITION_COLOR_SHADER = new RenderStateShard.ShaderStateShard(GameRenderer::getPositionColorShader);
    protected static final RenderStateShard.LayeringStateShard VIEW_OFFSET_Z_LAYERING = new RenderStateShard.LayeringStateShard("view_offset_z_layering", () -> {
        PoseStack posestack = RenderSystem.getModelViewStack();
        posestack.pushPose();
        posestack.scale(0.99975586F, 0.99975586F, 0.99975586F);
        RenderSystem.applyModelViewMatrix();
    }, () -> {
        PoseStack posestack = RenderSystem.getModelViewStack();
        posestack.popPose();
        RenderSystem.applyModelViewMatrix();
    });
    protected static final RenderStateShard.OutputStateShard ITEM_ENTITY_TARGET = new RenderStateShard.OutputStateShard("item_entity_target", () -> {
        if (Minecraft.useShaderTransparency()) {
            mc.levelRenderer.getItemEntityTarget().bindWrite(false);
        }

    }, () -> {
        if (Minecraft.useShaderTransparency()) {
            mc.getMainRenderTarget().bindWrite(false);
        }

    });
    protected static final RenderStateShard.TransparencyStateShard TRANSLUCENT_TRANSPARENCY = new RenderStateShard.TransparencyStateShard("translucent_transparency", () -> {
        RenderSystem.enableBlend();
        RenderSystem.blendFuncSeparate(GlStateManager.SourceFactor.SRC_ALPHA, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA, GlStateManager.SourceFactor.ONE, GlStateManager.DestFactor.ONE_MINUS_SRC_ALPHA);
    }, () -> {
        RenderSystem.disableBlend();
        RenderSystem.defaultBlendFunc();
    });
    protected static final RenderStateShard.ShaderStateShard RENDERTYPE_LINES_SHADER = new RenderStateShard.ShaderStateShard(GameRenderer::getRendertypeLinesShader);
    public static RenderType STRIP_LINES = RenderType.create(
            "no_depth_lines_strip",
            DefaultVertexFormat.POSITION_COLOR,
            VertexFormat.Mode.DEBUG_LINE_STRIP,
            256,
            false,false,
            RenderType.CompositeState.builder()
                    .setShaderState(RENDERTYPE_LINES_SHADER)
                    .setTransparencyState(NO_TRANSPARENCY)

                    .setCullState(NO_CULL)
                    .setDepthTestState(NO_DEPTH_TEST)

                    .createCompositeState(false)
    );

    protected static final RenderStateShard.WriteMaskStateShard COLOR_DEPTH_WRITE = new RenderStateShard.WriteMaskStateShard(true, true);
    public static RenderType LINES = RenderType.create(
            "no_depth_lines",
            DefaultVertexFormat.POSITION_COLOR,
            VertexFormat.Mode.DEBUG_LINES,
            256,
            false,false,
            RenderType.CompositeState.builder()
                    .setShaderState(POSITION_COLOR_SHADER)
                    .setTransparencyState(NO_TRANSPARENCY)

                    .setCullState(NO_CULL)
                    .setDepthTestState(NO_DEPTH_TEST)

                    .createCompositeState(false)
    );
    public static RenderType TEST = RenderType.create(
            "no_depth_test_lines",
            DefaultVertexFormat.POSITION_COLOR_NORMAL,
            VertexFormat.Mode.LINES,
            256,
            false,false,
            RenderType.CompositeState.builder()
                    .setShaderState(RENDERTYPE_LINES_SHADER)
                    .setLayeringState(VIEW_OFFSET_Z_LAYERING)
                    .setTransparencyState(TRANSLUCENT_TRANSPARENCY)
                    .setOutputState(ITEM_ENTITY_TARGET)
                    .setWriteMaskState(COLOR_DEPTH_WRITE)
                    .setWriteMaskState(COLOR_DEPTH_WRITE)
                    .setCullState(NO_CULL)
                    .setDepthTestState(NO_DEPTH_TEST)

                    .createCompositeState(false)
    );
    //    public static final RenderType.CompositeRenderType LINES = create("lines",
    //    DefaultVertexFormat.POSITION_COLOR_NORMAL,
    //    VertexFormat.Mode.LINES, 256,
//            RenderType.CompositeState.builder()
//                    .setShaderState(RENDERTYPE_LINES_SHADER)
//                    .setLineState(new RenderStateShard.LineStateShard(OptionalDouble.empty()))
//                    .setLayeringState(VIEW_OFFSET_Z_LAYERING)
//                    .setTransparencyState(TRANSLUCENT_TRANSPARENCY)
//                    .setOutputState(ITEM_ENTITY_TARGET)
//                    .setWriteMaskState(COLOR_DEPTH_WRITE)
//                    .setCullState(NO_CULL)
//                    .createCompositeState(false));
    public record Bone(Vec3 start, Vec3 end) {
    }


    public static void renderSkeleton(PoseStack poseStack, MultiBufferSource bufferSource, int packedLight, int packedOverlay, List<Bone> bones, int color) {
        poseStack.pushPose();
        Matrix4f matrix = poseStack.last().pose();
//        GL11.glLineWidth(5.0f);
//        RenderSystem.lineWidth(5.0f);
        VertexConsumer vertexConsumer = bufferSource.getBuffer(LINES);
        for (Bone bone : bones) {
            Vec3 start = bone.start();
            Vec3 end = bone.end();

            vertexConsumer.vertex(matrix, (float) start.x, (float) start.y, (float) start.z)
                    .color((color >> 16) & 0xFF, (color >> 8) & 0xFF, color & 0xFF, (color >> 24) & 0xFF)
                    .uv(0, 0)
                    .overlayCoords(packedOverlay)
                    .uv2(packedLight)
                    .normal(0.0f, 0, 0.0f)
                    .endVertex();


            vertexConsumer.vertex(matrix, (float) end.x, (float) end.y, (float) end.z)
                    .color((color >> 16) & 0xFF, (color >> 8) & 0xFF, color & 0xFF, (color >> 24) & 0xFF)
                    .uv(0, 0)
                    .overlayCoords(packedOverlay)
                    .uv2(packedLight)
                    .normal(0.0f, 0, 0.0f)
                    .endVertex();

        }
        poseStack.popPose();
    }

    public static BoneData translateBone(ModelPart part, ModelPart.Cube cube) {

            Vec3 v0 = new Vec3(cube.minX, cube.minY, cube.minZ);
            Vec3 v1 = new Vec3(cube.maxX, cube.minY, cube.minZ);
            Vec3 v2 = new Vec3(cube.maxX, cube.maxY, cube.minZ);
            Vec3 v3 = new Vec3(cube.minX, cube.maxY, cube.minZ);
            Vec3 v4 = new Vec3(cube.minX, cube.minY, cube.maxZ);
            Vec3 v5 = new Vec3(cube.maxX, cube.minY, cube.maxZ);
            Vec3 v6 = new Vec3(cube.maxX, cube.maxY, cube.maxZ);
            Vec3 v7 = new Vec3(cube.minX, cube.maxY, cube.maxZ);

            Vec3 center = new Vec3((cube.minX + cube.maxX) / 2.0, (cube.minY + cube.maxY) / 2.0, (cube.minZ + cube.maxZ) / 2.0);
            Vec3 frontCenter = new Vec3((v0.x + v1.x + v2.x + v3.x) / 4, (v0.y + v1.y + v2.y + v3.y) / 4, cube.minZ);
            Vec3 backCenter = new Vec3((v4.x + v5.x + v6.x + v7.x) / 4, (v4.y + v5.y + v6.y + v7.y) / 4, cube.maxZ);
            Vec3 leftCenter = new Vec3(cube.minX, (v0.y + v3.y + v4.y + v7.y) / 4, (v0.z + v3.z + v4.z + v7.z) / 4);
            Vec3 rightCenter = new Vec3(cube.maxX, (v1.y + v2.y + v5.y + v6.y) / 4, (v1.z + v2.z + v5.z + v6.z) / 4);
            Vec3 topCenter = new Vec3((v2.x + v3.x + v6.x + v7.x) / 4, cube.maxY, (v2.z + v3.z + v6.z + v7.z) / 4);
            Vec3 bottomCenter = new Vec3((v0.x + v1.x + v4.x + v5.x) / 4, cube.minY, (v0.z + v1.z + v4.z + v5.z) / 4);

            PoseStack stack = new PoseStack();
            stack.pushPose();
            stack.scale(0.0625f, 0.0625f, 0.0625f);
            stack.translate(part.x, part.y, part.z);
            part.translateAndRotate(stack);
            Matrix4f matrix = stack.last().pose();
            center = applyMatrixTransform(matrix, center);
            topCenter = applyMatrixTransform(matrix, topCenter);
            bottomCenter = applyMatrixTransform(matrix, bottomCenter);
            rightCenter = applyMatrixTransform(matrix, rightCenter);
            leftCenter = applyMatrixTransform(matrix, leftCenter);
            backCenter = applyMatrixTransform(matrix, backCenter);
            frontCenter = applyMatrixTransform(matrix, frontCenter);
            v0 = applyMatrixTransform(matrix, v0);
            v1 = applyMatrixTransform(matrix, v1);
            v2 = applyMatrixTransform(matrix, v2);
            v3 = applyMatrixTransform(matrix, v3);
            v4 = applyMatrixTransform(matrix, v4);
            v5 = applyMatrixTransform(matrix, v5);
            v6 = applyMatrixTransform(matrix, v6);
            v7 = applyMatrixTransform(matrix, v7);
            stack.popPose();
            return new BoneData(part, center, frontCenter, backCenter, leftCenter, rightCenter, topCenter, bottomCenter,
                    v0, v1, v2, v3, v4, v5, v6, v7);
    }
    public static Vec3 applyMatrixTransform(Matrix4f matrix, Vec3 vec) {
        Vector4f transformed = new Vector4f((float) vec.x, (float) vec.y, (float) vec.z, 1.0F);
        transformed.mul(matrix);
        return new Vec3(transformed.x(), transformed.y(), transformed.z());
    }
    public record BoneData(ModelPart modelPart, Vec3 center, Vec3 frontCenter, Vec3 backCenter, Vec3 leftCenter,
                           Vec3 rightCenter, Vec3 topCenter, Vec3 bottomCenter, Vec3 v0, Vec3 v1, Vec3 v2, Vec3 v3, Vec3 v4, Vec3 v5, Vec3 v6, Vec3 v7) {
    }

    public static void render3DEntityBoundingBox(PoseStack poseStack, Entity entity, int color, boolean damage, boolean fillMode, boolean wireframeMode, float fillAlpha) {
        if (entity == null || !(entity instanceof LivingEntity)) return;

        // 获取必要组件
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();
        Camera camera = renderManager.camera;
        double partialTicks = mc.getFrameTime();

        // 获取精确插值位置
        Vec3 entityPos = entity.getPosition((float) partialTicks)
                .subtract(camera.getPosition()); // 转换为相机相对坐标

        // 获取动态碰撞箱（包含所有状态）
        AABB bb = entity.getBoundingBoxForCulling();

        // 矩阵变换配置
        poseStack.pushPose();
        poseStack.translate(entityPos.x, entityPos.y, entityPos.z);

        // 碰撞箱尺寸计算
        float minX = (float) (bb.minX - entity.getX());
        float maxX = (float) (bb.maxX - entity.getX());
        float minY = (float) (bb.minY - entity.getY());
        float maxY = (float) (bb.maxY - entity.getY());
        float minZ = (float) (bb.minZ - entity.getZ());
        float maxZ = (float) (bb.maxZ - entity.getZ());

        // 颜色逻辑 - 增强的受伤渐变效果
        int startColor = color;
        int endColor = color;

        // 检查玩家受伤状态以调整颜色 - 平滑渐变
        if (damage && entity instanceof LivingEntity living) {
            if (living.hurtTime > 0) {
                float hurtProgress = (float) living.hurtTime / 10.0f;

                // 提取原始颜色分量
                float a = (color >> 24 & 0xFF) / 255f;
                float r = (color >> 16 & 0xFF) / 255f;
                float g = (color >> 8 & 0xFF) / 255f;
                float b = (color & 0xFF) / 255f;

                // 调整颜色 - 更多红色，减少绿色和蓝色
                g *= (1.0f - hurtProgress * 0.8f);
                b *= (1.0f - hurtProgress * 0.8f);
                r = Math.min(1.0f, r + (1.0f - r) * hurtProgress * 0.8f);

                // 转回整数颜色格式
                int newColor = ((int)(a * 255) << 24) | ((int)(r * 255) << 16) | ((int)(g * 255) << 8) | (int)(b * 255);

                // 创建渐变效果
                startColor = newColor;

                // 更深的红色作为结束颜色
                float darkerR = Math.max(0.4f, r * 0.7f);
                int darkerColor = ((int)(a * 255) << 24) | ((int)(darkerR * 255) << 16) | ((int)(g * 255) << 8) | (int)(b * 255);
                endColor = darkerColor;
            }
        }

        // 启用3D渲染状态
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();
        RenderSystem.setShader(GameRenderer::getPositionColorShader);

        // 绘制填充模式
        if (fillMode) {
            drawFilledBoundingBox(poseStack, minX, maxX, minY, maxY, minZ, maxZ, startColor, endColor, fillAlpha);
        }

        // 绘制线框模式
        if (wireframeMode) {
            // 禁用深度测试，确保方框始终可见
            RenderSystem.disableDepthTest();
            RenderSystem.defaultBlendFunc();
            drawBoundingBox(poseStack, minX, maxX, minY, maxY, minZ, maxZ, startColor, endColor);
        }

        // 恢复渲染状态
        RenderSystem.disableBlend();
        poseStack.popPose();
    }
    
    // 绘制实体边界框线框
    private static void drawBoundingBox(PoseStack poseStack, float minX, float maxX, float minY, float maxY, float minZ, float maxZ, int startColor, int endColor) {
        Matrix4f matrix = poseStack.last().pose();
        VertexConsumer buffer = mc.renderBuffers().bufferSource().getBuffer(LINES);
        
        float startA = (startColor >> 24 & 0xFF) / 255.0F;
        float startR = (startColor >> 16 & 0xFF) / 255.0F;
        float startG = (startColor >> 8 & 0xFF) / 255.0F;
        float startB = (startColor & 0xFF) / 255.0F;
        
        float endA = (endColor >> 24 & 0xFF) / 255.0F;
        float endR = (endColor >> 16 & 0xFF) / 255.0F;
        float endG = (endColor >> 8 & 0xFF) / 255.0F;
        float endB = (endColor & 0xFF) / 255.0F;
        
        // 底部
        drawLine(buffer, matrix, minX, minY, minZ, maxX, minY, minZ, startR, startG, startB, startA);
        drawLine(buffer, matrix, maxX, minY, minZ, maxX, minY, maxZ, startR, startG, startB, startA);
        drawLine(buffer, matrix, maxX, minY, maxZ, minX, minY, maxZ, startR, startG, startB, startA);
        drawLine(buffer, matrix, minX, minY, maxZ, minX, minY, minZ, startR, startG, startB, startA);
        
        // 顶部
        drawLine(buffer, matrix, minX, maxY, minZ, maxX, maxY, minZ, endR, endG, endB, endA);
        drawLine(buffer, matrix, maxX, maxY, minZ, maxX, maxY, maxZ, endR, endG, endB, endA);
        drawLine(buffer, matrix, maxX, maxY, maxZ, minX, maxY, maxZ, endR, endG, endB, endA);
        drawLine(buffer, matrix, minX, maxY, maxZ, minX, maxY, minZ, endR, endG, endB, endA);
        
        // 连接线
        drawLine(buffer, matrix, minX, minY, minZ, minX, maxY, minZ, startR, startG, startB, startA, endR, endG, endB, endA);
        drawLine(buffer, matrix, maxX, minY, minZ, maxX, maxY, minZ, startR, startG, startB, startA, endR, endG, endB, endA);
        drawLine(buffer, matrix, maxX, minY, maxZ, maxX, maxY, maxZ, startR, startG, startB, startA, endR, endG, endB, endA);
        drawLine(buffer, matrix, minX, minY, maxZ, minX, maxY, maxZ, startR, startG, startB, startA, endR, endG, endB, endA);
        
        mc.renderBuffers().bufferSource().endBatch(LINES);
    }
    
    // 绘制填充的边界框
    private static void drawFilledBoundingBox(PoseStack poseStack, float minX, float maxX, float minY, float maxY, float minZ, float maxZ, int startColor, int endColor, float alpha) {
        Matrix4f matrix = poseStack.last().pose();
        
        // 提取颜色分量
        float startA = alpha;
        float startR = (startColor >> 16 & 0xFF) / 255.0F;
        float startG = (startColor >> 8 & 0xFF) / 255.0F;
        float startB = (startColor & 0xFF) / 255.0F;
        
        float endA = alpha;
        float endR = (endColor >> 16 & 0xFF) / 255.0F;
        float endG = (endColor >> 8 & 0xFF) / 255.0F;
        float endB = (endColor & 0xFF) / 255.0F;
        
        // 开始绘制
        RenderSystem.enableBlend();
        RenderSystem.disableCull();
        RenderSystem.defaultBlendFunc();
        
        // 用三角形绘制各个面
        drawQuad(poseStack, minX, maxX, minY, maxY, minZ, startR, startG, startB, startA, endR, endG, endB, endA, 1); // 前面
        drawQuad(poseStack, minX, maxX, minY, maxY, maxZ, startR, startG, startB, startA, endR, endG, endB, endA, -1); // 后面
        drawQuad(poseStack, minX, minX, minY, maxY, minZ, maxZ, startR, startG, startB, startA, endR, endG, endB, endA, 2); // 左面
        drawQuad(poseStack, maxX, maxX, minY, maxY, minZ, maxZ, startR, startG, startB, startA, endR, endG, endB, endA, -2); // 右面
        drawQuad(poseStack, minX, maxX, maxY, maxY, minZ, maxZ, endR, endG, endB, endA, 3); // 顶面
        drawQuad(poseStack, minX, maxX, minY, minY, minZ, maxZ, startR, startG, startB, startA, -3); // 底面
        
        // 恢复状态
        RenderSystem.enableCull();
        RenderSystem.disableBlend();
    }
    
    // 绘制线
    private static void drawLine(VertexConsumer buffer, Matrix4f matrix, float x1, float y1, float z1, float x2, float y2, float z2, float r, float g, float b, float a) {
        buffer.vertex(matrix, x1, y1, z1).color(r, g, b, a).normal(0, 1, 0).endVertex();
        buffer.vertex(matrix, x2, y2, z2).color(r, g, b, a).normal(0, 1, 0).endVertex();
    }
    
    // 绘制带渐变的线
    private static void drawLine(VertexConsumer buffer, Matrix4f matrix, float x1, float y1, float z1, float x2, float y2, float z2, 
                               float r1, float g1, float b1, float a1, float r2, float g2, float b2, float a2) {
        buffer.vertex(matrix, x1, y1, z1).color(r1, g1, b1, a1).normal(0, 1, 0).endVertex();
        buffer.vertex(matrix, x2, y2, z2).color(r2, g2, b2, a2).normal(0, 1, 0).endVertex();
    }
    
    // 绘制矩形面
    private static void drawQuad(PoseStack poseStack, float x1, float x2, float y1, float y2, float z, 
                               float r, float g, float b, float a, float r2, float g2, float b2, float a2, int normalDirection) {
        Matrix4f matrix = poseStack.last().pose();
        
        float nx = 0, ny = 0, nz = 0;
        if (normalDirection == 1) nz = 1;
        else if (normalDirection == -1) nz = -1;
        else if (normalDirection == 2) nx = 1;
        else if (normalDirection == -2) nx = -1;
        else if (normalDirection == 3) ny = 1;
        else if (normalDirection == -3) ny = -1;
        
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        VertexConsumer consumer = mc.renderBuffers().bufferSource().getBuffer(TEST);
        
        // 绘制三角形1
        consumer.vertex(matrix, x1, y1, z).color(r, g, b, a).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x2, y1, z).color(r, g, b, a).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x2, y2, z).color(r2, g2, b2, a2).normal(nx, ny, nz).endVertex();
        
        // 绘制三角形2
        consumer.vertex(matrix, x1, y1, z).color(r, g, b, a).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x2, y2, z).color(r2, g2, b2, a2).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x1, y2, z).color(r2, g2, b2, a2).normal(nx, ny, nz).endVertex();
        
        mc.renderBuffers().bufferSource().endBatch(TEST);
    }
    
    // 绘制两个z坐标不同的面（用于侧面）
    private static void drawQuad(PoseStack poseStack, float x1, float x2, float y1, float y2, float z1, float z2,
                               float r, float g, float b, float a, float r2, float g2, float b2, float a2, int normalDirection) {
        Matrix4f matrix = poseStack.last().pose();
        
        float nx = 0, ny = 0, nz = 0;
        if (normalDirection == 2) nx = 1;
        else if (normalDirection == -2) nx = -1;
        
        RenderSystem.setShader(GameRenderer::getPositionColorShader);
        VertexConsumer consumer = mc.renderBuffers().bufferSource().getBuffer(TEST);
        
        // 绘制三角形1
        consumer.vertex(matrix, x1, y1, z1).color(r, g, b, a).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x2, y1, z2).color(r, g, b, a).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x2, y2, z2).color(r2, g2, b2, a2).normal(nx, ny, nz).endVertex();
        
        // 绘制三角形2
        consumer.vertex(matrix, x1, y1, z1).color(r, g, b, a).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x2, y2, z2).color(r2, g2, b2, a2).normal(nx, ny, nz).endVertex();
        consumer.vertex(matrix, x1, y2, z1).color(r2, g2, b2, a2).normal(nx, ny, nz).endVertex();
        
        mc.renderBuffers().bufferSource().endBatch(TEST);
    }
    
    // 绘制单色矩形面
    private static void drawQuad(PoseStack poseStack, float x1, float x2, float y1, float y2, float z1, float z2, float r, float g, float b, float a, int normalDirection) {
        drawQuad(poseStack, x1, x2, y1, y2, z1, z2, r, g, b, a, r, g, b, a, normalDirection);
    }
}
