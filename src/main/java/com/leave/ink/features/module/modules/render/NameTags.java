package com.leave.ink.features.module.modules.render;

import com.darkmagician6.eventapi.EventTarget;
import com.leave.ink.Main;
import com.leave.ink.events.EventRender3D;
import com.leave.ink.events.EventRender2D;
import com.leave.ink.events.hud.EventSkia2D;
import com.leave.ink.features.module.Category;
import com.leave.ink.features.module.Module;
import com.leave.ink.features.module.annotation.ModuleInfo;
import com.leave.ink.features.module.modules.other.ClayGuns;
import com.leave.ink.features.module.modules.other.KillerCheck;
import com.leave.ink.features.module.modules.settings.Targets;
import com.leave.ink.features.module.modules.world.AntiBot;
import com.leave.ink.features.setting.annotation.SettingInfo;
import com.leave.ink.features.setting.attribute.SettingAttribute;
import com.leave.ink.features.setting.settings.BooleanSetting;
import com.leave.ink.features.setting.settings.ModeSetting;
import com.leave.ink.features.setting.settings.NumberSetting;
import com.leave.ink.language.Language;
import com.leave.ink.language.Text;
import java.util.Arrays;
import com.leave.ink.ui.skija.CanvasStack;
import com.leave.ink.ui.skija.SkiaRender;
import com.leave.ink.ui.skija.font.SkiaFontManager;
import com.leave.ink.utils.render.projectiles.world.ProjectionSystem;
import com.leave.ink.utils.render.projectiles.world.ProjectionUtils;
import lombok.Getter;
import net.minecraft.util.Mth;
import org.joml.Matrix4f;
import com.mojang.blaze3d.systems.RenderSystem;
import com.leave.ink.ui.skija.utils.Skia3DRenderer;
import com.leave.ink.utils.Utils;
import com.leave.ink.utils.fonts.FontRenderers;
import com.leave.ink.utils.manager.HeypixelManager;
import com.leave.ink.utils.player.PlayerUtils;
import com.leave.ink.utils.render.RenderUtils;
import com.mojang.blaze3d.vertex.PoseStack;
import com.mojang.math.Axis;
import net.minecraft.ChatFormatting;
import net.minecraft.world.phys.Vec3;
import net.minecraft.client.renderer.entity.EntityRenderDispatcher;
import net.minecraft.world.entity.Entity;
import net.minecraft.world.entity.LivingEntity;
import net.minecraft.world.entity.player.Player;
import net.minecraft.world.item.ItemStack;

import java.awt.*;
import java.text.DecimalFormat;
import java.util.Map;

@ModuleInfo(name = {
        @Text(label = "NameTags", language = Language.English),
        @Text(label = "名字标签", language = Language.Chinese)
}, category = Category.Render)
public class NameTags extends Module {

    @SettingInfo(name = {
            @Text(label = "Health", language = Language.English),
            @Text(label = "血量 ", language = Language.Chinese)
    })
    private final BooleanSetting healthValue = new BooleanSetting(true);
    @SettingInfo(name = {
            @Text(label = "Distance", language = Language.English),
            @Text(label = "距离 ", language = Language.Chinese)
    })
    private final BooleanSetting distanceValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Armor", language = Language.English),
            @Text(label = "盔甲 ", language = Language.Chinese)
    })
    private final BooleanSetting armorValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Scale", language = Language.English),
            @Text(label = "大小 ", language = Language.Chinese)
    })
    private final NumberSetting scaleValue = new NumberSetting(1.5F, 1F, 4F, "#.00");
    @SettingInfo(name = {
            @Text(label = "Blur", language = Language.English),
            @Text(label = "模糊 ", language = Language.Chinese)
    })
    private final BooleanSetting blurValue = new BooleanSetting(false);
    @SettingInfo(name = {
            @Text(label = "Self", language = Language.English),
            @Text(label = "自己 ", language = Language.Chinese)
    })
    private final BooleanSetting self = new BooleanSetting(false);

    @SettingInfo(name = {
            @Text(label = "Dis Mc Name", language = Language.English),
            @Text(label = "禁用原版标签", language = Language.Chinese)
    })
    private final BooleanSetting disableVanilla = new BooleanSetting(true);

    @SettingInfo(name = {
            @Text(label = "Skia X Offset", language = Language.English),
            @Text(label = "Skia X 偏移", language = Language.Chinese)
    })
    private final NumberSetting skiaXOffset = new NumberSetting(-8F, -50F, 50F, "#.0");

    @SettingInfo(name = {
            @Text(label = "Skia Y Offset", language = Language.English),
            @Text(label = "Skia Y 偏移", language = Language.Chinese)
    })
    private final NumberSetting skiaYOffset = new NumberSetting(6F, -50F, 50F, "#.0");

    @SettingInfo(name = {
            @Text(label = "UI Style", language = Language.English),
            @Text(label = "UI风格", language = Language.Chinese)
    })
    private final ModeSetting uiStyle = new ModeSetting("Modern", Arrays.asList("Modern", "Classic"));

    @SettingInfo(name = {
            @Text(label = "Render Mode", language = Language.English),
            @Text(label = "渲染模式", language = Language.Chinese)
    })
    private final ModeSetting renderMode = new ModeSetting("MC", Arrays.asList("MC", "Skia"),
            new SettingAttribute<>(uiStyle, "Skia"),
            new SettingAttribute<>(skiaXOffset, "Skia"),
            new SettingAttribute<>(skiaYOffset, "Skia"),
            new SettingAttribute<>(blurValue, "Skia")
    );


    private static class RenderState extends ProjectionSystem.ProjectionData {
        float x, y, width, height;
        String mainText, subText;
        float healthRate;
        LivingEntity entity;
        boolean isValid = false;

        void update(float x, float y, float width, float height, String mainText, String subText, float healthRate, LivingEntity entity) {
            this.x = x;
            this.y = y;
            this.width = width;
            this.height = height;
            this.mainText = mainText;
            this.subText = subText;
            this.healthRate = healthRate;
            this.entity = entity;
            this.isValid = true;
        }

        void invalidate() {
            this.isValid = false;
            this.entity = null;
        }
    }

    private static class SkiaRenderState extends ProjectionSystem.ProjectionScreenData
    {
        @Getter
        private LivingEntity livingEntity;
        @Getter
        private String mainText;
        @Getter
        private float scale;

        public SkiaRenderState(Vec3 pos, LivingEntity livingEntity, String mainText, float scale) {
            super(pos);
            this.livingEntity = livingEntity;
            this.mainText = mainText;
            this.scale = scale;
        }
    }
    private final RenderState currentRenderState = new RenderState();

    public NameTags() {
        registerSetting(renderMode, healthValue, distanceValue, armorValue, scaleValue, self, disableVanilla);
    }

    @EventTarget(4)
    public void onRender3D(EventRender3D event) {
        //if(!renderMode.is("MC"))return;
        if (mc.player == null)
            return;
        for (Entity entity : mc.level.entitiesForRendering()) {
            if (entity instanceof LivingEntity) {
                if (entity == mc.player) {
                    if (mc.options.getCameraType().isFirstPerson()) continue;
                    if (!self.getValue()) {
                        continue;
                    }
                }

                if (!Utils.isValidEntity((LivingEntity) entity))
                    continue;

                renderNameTag((LivingEntity) entity, Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText()), event.getPoseStack());
            }
        }
    }
    @EventTarget
    public void onSkia(EventSkia2D event) {
        if(!renderMode.is("Skia"))return;
        if (mc.player == null)
            return;
        for (Map.Entry<Integer, ProjectionSystem.ProjectionData> integerProjectionDataEntry : projectionSystem.getDataMap().entrySet()) {
            SkiaRenderState projectionScreenData = (SkiaRenderState) integerProjectionDataEntry.getValue();
//            if (!renderState.isValid) continue;
            //System.out.println("11111111111");
            Vec3 screenPos = projectionScreenData.pos;
            CanvasStack canvasStack = event.getCanvasStack();
            canvasStack.push();
           //  canvasStack.getCanvas().scale(projectionScreenData.scale, projectionScreenData.scale);
            if (uiStyle.getValue().equals("Modern")) {
                renderModernSkiaUI(event.getCanvasStack(), screenPos, projectionScreenData.livingEntity, projectionScreenData.mainText);
            } else {
                renderClassicSkiaUI(event.getCanvasStack(), screenPos);
            }
            canvasStack.pop();
            //renderWithSkia3DRenderer(event.getCanvasStack(), renderState.entity);
//            renderNameTagSkia((LivingEntity) entity, Utils.getStringFromFormattedCharSequence(entity.getDisplayName().getVisualOrderText()), event.getCanvasStack());
        }

    }
    @EventTarget(4)
    public void onRender2D(EventRender2D event) {
        if (mc.player == null || mc.level == null)
            return;
        if (armorValue.getValue() && renderMode.getValue().equals("Skia")) {
            for (Entity entity : mc.level.entitiesForRendering()) {
                if (entity instanceof Player) {
                    if (entity == mc.player) {
                        if (mc.options.getCameraType().isFirstPerson()) continue;
                        if (!self.getValue()) {
                            continue;
                        }
                    }

                    if (!Utils.isValidEntity((LivingEntity) entity))
                        continue;

                    float partialTicks = mc.getFrameTime();

                    double x = Mth.lerp(partialTicks, entity.xOld, entity.getX());
                    double y = Mth.lerp(partialTicks, entity.yOld, entity.getY()) + entity.getBbHeight() + 0.5;
                    double z = Mth.lerp(partialTicks, entity.zOld, entity.getZ());

                    Vec3 screenPos = Main.INSTANCE.projection.projectToScreen(new Vec3(x, y, z));
                   // Vec3 screenPos = getEntityScreenPosition((LivingEntity) entity);
                    if (screenPos != null) {
                        //renderEquipment2D(event, (Player) entity, screenPos);
                    }
                }
            }
        }
    }

    private final DecimalFormat dFormat = new DecimalFormat("0.0");
    private ProjectionSystem<Integer> projectionSystem = new ProjectionSystem<>();
    //update position
    private void renderNameTag(LivingEntity entity, String tag, PoseStack poseStack) {
        var clayGuns = (ClayGuns) Main.INSTANCE.moduleManager.getModule("CSGuns");
        boolean bot = AntiBot.isBot(entity);
        boolean killer = KillerCheck.isKiller(entity);
        boolean friend = false;
        boolean team = Targets.isTeam(entity) || (clayGuns.isEnable() && clayGuns.teamName.contains(entity.getName().getString()));
        float a = HeypixelManager.getEntityHealth(entity);

        String nameColor = bot ? "§3" : entity.isInvisible() ? "§6" : entity.isCrouching() ? "§4" : ChatFormatting.WHITE + "";
        String distanceText = distanceValue.getValue() ? " §7" + (int) mc.player.distanceTo(entity) + "m" : "";
        String healthDisplay = dFormat.format(a);
        String healthText = healthValue.getValue() ? "§7§cHP: " + healthDisplay : "";
        String botText = bot ? " §c§lBot " : "";
        String killerText = killer ? " §cKiller " : "";
        String friendText = friend ? " §c§lFriend " : "";
        String teamText = team ? ChatFormatting.AQUA + "[Team] " : "";
        String text = healthText + distanceText;

        String attitudeText = "";
        if (entity instanceof Player player) {
            boolean isGodAxe = PlayerUtils.isHoldingGodAxe(player);
            attitudeText = isGodAxe ? " §4§l[GodAxe]" : "";
        }
        String mainText = teamText + nameColor + tag + botText + killerText + friendText + attitudeText;
        poseStack.pushPose();
        EntityRenderDispatcher renderManager = mc.getEntityRenderDispatcher();

        poseStack.translate(
                entity.xOld + (entity.getX() - entity.xOld) * mc.getFrameTime() - renderManager.camera.getPosition().x,
                entity.yOld + (entity.getY() - entity.yOld) * mc.getFrameTime() - renderManager.camera.getPosition().y + entity.getEyeHeight() + 0.55,
                entity.zOld + (entity.getZ() - entity.zOld) * mc.getFrameTime() - renderManager.camera.getPosition().z
        );

        poseStack.mulPose(Axis.YP.rotationDegrees(-renderManager.camera.getYRot()));
        poseStack.mulPose(Axis.XP.rotationDegrees(renderManager.camera.getXRot()));

        float distance = (float) (mc.player.distanceTo(entity) * 0.25);
        if (distance < 1F) distance = 1F;
        float scale = distance / 100f * scaleValue.getValue().floatValue();
        poseStack.scale(-scale, -scale, scale);
        float width = Math.max(FontRenderers.misans18.getStringWidth(text), FontRenderers.misans18.getStringWidth(mainText)) * 0.5f;
        float height = FontRenderers.misans18.getHeight() + 16;
        float rate = HeypixelManager.getHealthRate(entity);

        currentRenderState.update(-width - 2F, -(height), width * 2 + 4F, height, mainText, text, rate, entity);
        //////////////////////////////////////储存skia渲染数据
        Vec3 worldPos = ProjectionUtils.calculateWorldPosition(entity, 0.55f);
        float scale2 = calculateScale(mc.player.distanceTo(entity), 0.25f, 1.0f,1.5f);
        Vec3 sc = projectionSystem.projectToScreen(worldPos);
        projectionSystem.update(entity.getId(), new SkiaRenderState(sc, entity, mainText, scale2));
        ///////////////////////////////////
        //renderWithSkia3DRenderer(new CanvasStack(Main.INSTANCE.skiaProcess.skiaUtils), entity);
        if(renderMode.is("MC"))
         renderWithVanilla(poseStack, mainText, text, entity);


        currentRenderState.invalidate();
        RenderSystem.enableDepthTest();
        RenderSystem.disableBlend();
        poseStack.popPose();
    }
    private static float calculateScale(float distance, float scaleMultiplier, float minDistance, float baseScale) {

        float adjustedDistance = distance * scaleMultiplier;
        if (adjustedDistance < minDistance) {
            adjustedDistance = minDistance;
        }

        return adjustedDistance / 100f * baseScale;
    }


    private void renderModernSkiaUI(CanvasStack canvasStack, Vec3 screenPos,LivingEntity entity, String mainText) {
        canvasStack.push();
        String mainDisplayText = mainText;
        StringBuilder subTextBuilder = new StringBuilder();
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(entity);
            subTextBuilder.append("§7§c").append(dFormat.format(health)).append("HP");
        }
        if (distanceValue.getValue()) {
            if (subTextBuilder.length() > 0) subTextBuilder.append(" ");
            int distance = (int) mc.player.distanceTo(entity);
            subTextBuilder.append("§7").append(distance).append("m");
        }
        String subDisplayText = subTextBuilder.toString();
        float baseMainTextWidth = SkiaRender.getCleanTextWidth(mainDisplayText, 14);
        float baseSubTextWidth = !subDisplayText.isEmpty() ? SkiaRender.getCleanTextWidth(subDisplayText, 12) : 0;
        float baseTextWidth = Math.max(baseMainTextWidth, baseSubTextWidth);
        float baseTextHeight = SkiaFontManager.getDefaultFont14().getActualHeight();

        float manualScale = scaleValue.getValue().floatValue() / 1.5f;
        float textWidth = baseTextWidth * manualScale;
        float textHeight = baseTextHeight * manualScale;

        float screenX = (float) screenPos.x;
        float screenY = (float) screenPos.y;

        float xOffset = skiaXOffset.getValue().floatValue();
        float yOffset = skiaYOffset.getValue().floatValue();

        float textCenterX = screenX + xOffset;
        float textCenterY = screenY + yOffset;
        float textLeft = textCenterX - textWidth / 2f;
        float textTop = textCenterY - textHeight / 2f;
        float padding = 1.4f;
        float totalHeight = baseTextHeight * manualScale;
        if (!subDisplayText.isEmpty()) {
            totalHeight += SkiaFontManager.getDefaultFont12().getActualHeight() * manualScale + 2f;
        }
        
        float bgX = textLeft - padding;
        float bgY = textTop - padding;
        float bgWidth = textWidth + (padding * 2);
        float bgHeight = totalHeight + (padding * 2);
        float cornerRadius = 4f;
        if (blurValue.getValue()) {
            float blurRadius = 8f;
            SkiaRender.drawBlurRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, blurRadius);
            int glassColor = new Color(240, 240, 240, 60).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, glassColor);
        } else {
            int backgroundColor = new Color(200, 200, 200, 120).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, backgroundColor);
        }
        canvasStack.push();
        canvasStack.canvas.scale(manualScale, manualScale);
        float scaledMainX = textLeft / manualScale;
        float scaledMainY = textTop / manualScale;
        SkiaRender.drawColoredText(canvasStack, mainDisplayText, scaledMainX, scaledMainY, 14, false);
        canvasStack.pop();
        if (!subDisplayText.isEmpty()) {
            float subTextY = textTop + (SkiaFontManager.getDefaultFont14().getActualHeight() * manualScale) + 2f;
            canvasStack.push();
            canvasStack.canvas.scale(manualScale * 0.85f, manualScale * 0.85f); // 子文本稍小一些
            float scaledSubX = textLeft / (manualScale * 0.85f);
            float scaledSubY = subTextY / (manualScale * 0.85f);
            SkiaRender.drawColoredText(canvasStack, subDisplayText, scaledSubX, scaledSubY, 12, false);
            canvasStack.pop();
        }
        if (armorValue.getValue() && entity instanceof Player) {
            //renderModernArmorSkia(canvasStack, (Player) currentRenderState.entity, textCenterX, textTop, totalHeight, manualScale);
        }

        canvasStack.pop();
    }
    private void renderClassicSkiaUI(CanvasStack canvasStack, Vec3 screenPos) {
        canvasStack.push();

        String playerName = Utils.getStringFromFormattedCharSequence(currentRenderState.entity.getDisplayName().getVisualOrderText());
        StringBuilder classicTextBuilder = new StringBuilder();
        classicTextBuilder.append(playerName);
        
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(currentRenderState.entity);
            classicTextBuilder.append(" §7[§c").append(dFormat.format(health)).append("HP§7]");
        }
        
        if (distanceValue.getValue()) {
            int distance = (int) mc.player.distanceTo(currentRenderState.entity);
            classicTextBuilder.append(" §7[§f").append(distance).append("m§7]");
        }
        
        String classicText = classicTextBuilder.toString();
        float baseTextWidth = SkiaRender.getCleanTextWidth(classicText, 14);
        float baseTextHeight = SkiaFontManager.getDefaultFont14().getActualHeight();

        float manualScale = scaleValue.getValue().floatValue() / 1.5f;
        float textWidth = baseTextWidth * manualScale;
        float textHeight = baseTextHeight * manualScale;

        float screenX = (float) screenPos.x;
        float screenY = (float) screenPos.y;

        float xOffset = skiaXOffset.getValue().floatValue();
        float yOffset = skiaYOffset.getValue().floatValue();

        float textCenterX = screenX + xOffset;
        float textCenterY = screenY + yOffset;
        float textLeft = textCenterX - textWidth / 2f;
        float textTop = textCenterY - textHeight / 2f;
        float padding = 1.4f;
        float bgX = textLeft - padding;
        float bgY = textTop - padding;
        float bgWidth = textWidth + (padding * 2);
        float bgHeight = textHeight + (padding * 2);
        float cornerRadius = 4f;

        if (blurValue.getValue()) {
            float blurRadius = 8f;
            SkiaRender.drawBlurRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, blurRadius);
            int glassColor = new Color(240, 240, 240, 60).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, glassColor);
        } else {
            int backgroundColor = new Color(200, 200, 200, 120).getRGB();
            SkiaRender.drawRoundedRect(canvasStack, bgX, bgY, bgWidth, bgHeight, cornerRadius, backgroundColor);
        }
        canvasStack.push();
        canvasStack.canvas.scale(manualScale, manualScale);
        float scaledX = textLeft / manualScale;
        float scaledY = textTop / manualScale;
        SkiaRender.drawColoredText(canvasStack, classicText, scaledX, scaledY, 14, false);
        canvasStack.pop();
        if (armorValue.getValue() && currentRenderState.entity instanceof Player) {
          //  renderClassicArmorSkia(canvasStack, (Player) currentRenderState.entity, textCenterX, textTop, textHeight, manualScale);
        }

        canvasStack.pop();
    }



    private String buildDisplayText(LivingEntity entity, String entityName) {
        StringBuilder text = new StringBuilder();
        text.append(entityName);
        if (healthValue.getValue()) {
            float health = HeypixelManager.getEntityHealth(entity);
            text.append(" [").append(dFormat.format(health)).append("HP]");
        }
        if (distanceValue.getValue()) {
            int distance = (int) mc.player.distanceTo(entity);
            text.append(" [").append(distance).append("m]");
        }

        return text.toString();
    }



    private void renderWithVanilla(PoseStack poseStack, String mainText, String text, LivingEntity entity) {
        RenderSystem.enableBlend();
        RenderSystem.defaultBlendFunc();
        RenderSystem.disableDepthTest();

        float mainTextWidth = FontRenderers.misans18.getStringWidth(mainText);
        float subTextWidth = FontRenderers.misans14.getStringWidth(text);
        float width = Math.max(mainTextWidth, subTextWidth) * 0.5f;
        float height = FontRenderers.misans18.getHeight();

        RenderUtils.drawRect(poseStack, -width - 2F, 0, width + 4F, -(height + 16),
                new Color(24, 24, 24, 160).getRGB());

        Color color = getHealthColor(currentRenderState.healthRate);
        float healthBarWidth = (width + 4F - (-width - 2F)) * currentRenderState.healthRate; // 原始计算方式
        RenderUtils.drawRect(poseStack, -width - 2F, -1.5f, healthBarWidth, 1.5f, color.getRGB());

        FontRenderers.misans18.drawString(poseStack, mainText, 1F - width, -20f, Color.WHITE.getRGB());
        FontRenderers.misans14.drawString(poseStack, text, 1F - width, -9F, Color.WHITE.getRGB());
        if (armorValue.getValue() && entity instanceof Player) {
            renderArmorInVanilla(poseStack, entity, width);
        }
    }

    private void renderArmorInVanilla(PoseStack poseStack, LivingEntity entity, float textWidth) {
        java.util.List<ItemStack> armorItems = new java.util.ArrayList<>();

        if (entity instanceof Player player) {
            for (ItemStack armorItem : player.getArmorSlots()) {
                armorItems.add(0, armorItem);
            }
            armorItems.add(player.getMainHandItem());
            armorItems.add(player.getOffhandItem());
        }
        armorItems.removeIf(ItemStack::isEmpty);

        if (armorItems.isEmpty()) return;
        float itemSize = 12f;
        float itemSpacing = 1f;
        float totalWidth = armorItems.size() * itemSize + (armorItems.size() - 1) * itemSpacing;
        float startX = -totalWidth / 2f;
        float startY = -35f;
        for (int i = 0; i < armorItems.size(); i++) {
            ItemStack item = armorItems.get(i);
            if (!item.isEmpty()) {
                float itemX = startX + i * (itemSize + itemSpacing);
                float itemY = startY;
                RenderUtils.renderAndDecorateItem(poseStack, item, (int)itemX, (int)itemY);
            }
        }
    }


    private Color getHealthColor(float rate) {
        if (rate > 0.75) {
            return Color.GREEN;
        } else if (rate <= 0.75f && rate >= 0.5f) {
            return Color.ORANGE;
        } else if (rate <= 0.5f && rate >= 0.25f) {
            return new Color(255, 97, 24);
        } else {
            return Color.RED;
        }
    }

    public boolean isDisableVanilla() {
        return disableVanilla.getValue();
    }


}

